services:
  mariadb:
    image: mariadb:10.5
    environment:
      - MARIADB_DATABASE=n8n
      - MARIADB_ROOT_PASSWORD=password
      - MARIADB_MYSQL_LOCALHOST_USER=true
    ports:
      - 3306:3306
    tmpfs:
      - /var/lib/mysql

  mysql-8.0.13:
    image: mysql:8.0.13
    environment:
      - MYSQL_DATABASE=n8n
      - MYSQL_ROOT_PASSWORD=password
    ports:
      - 3306:3306
    tmpfs:
      - /var/lib/mysql

  mysql-8.4:
    image: mysql:8.4
    environment:
      - MYSQL_DATABASE=n8n
      - MYSQL_ROOT_PASSWORD=password
    ports:
      - 3306:3306
    tmpfs:
      - /var/lib/mysql

  postgres:
    image: postgres:16
    restart: always
    environment:
      - POSTGRES_DB=n8n
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    ports:
      - 5432:5432
    tmpfs:
      - /var/lib/postgresql/data
