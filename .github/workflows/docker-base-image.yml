name: Docker Base Image CI

on:
  workflow_dispatch:
    inputs:
      node_version:
        description: 'Node.js version to build this image with.'
        type: choice
        required: true
        default: '20'
        options:
          - '20'
          - '22'
          - '24'

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@b4ffde65f46336ab88eb53be808477a3936bae11 # v4.1.1

      - name: Set up QEMU
        uses: docker/setup-qemu-action@53851d14592bedcffcf25ea515637cff71ef929a # v3.3.0

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@6524bf65af31da8d45b59e8c27de4bd072b392f5 # v3.8.0

      - name: Login to GitHub Container Registry
        uses: docker/login-action@9780b0c442fbb1117ed29e0efdff1e18412f7567 # v3.3.0
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Login to DockerHub
        uses: docker/login-action@9780b0c442fbb1117ed29e0efdff1e18412f7567 # v3.3.0
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Build
        uses: docker/build-push-action@b32b51a8eda65d6793cd0494a773d4f6bcef32dc # v6.11.0
        env:
          DOCKER_BUILD_SUMMARY: false
        with:
          context: .
          file: ./docker/images/n8n-base/Dockerfile
          build-args: |
            NODE_VERSION=${{github.event.inputs.node_version}}
          platforms: linux/amd64,linux/arm64
          provenance: false
          push: true
          tags: |
            ${{ secrets.DOCKER_USERNAME }}/base:${{ github.event.inputs.node_version }}
            ghcr.io/${{ github.repository_owner }}/base:${{ github.event.inputs.node_version }}
