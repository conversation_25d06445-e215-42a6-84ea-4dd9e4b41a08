name: Chromatic

on:
  schedule:
    - cron: '0 0 * * *'
  workflow_dispatch:
  pull_request_review:
    types: [submitted]

concurrency:
  group: chromatic-${{ github.event.pull_request.number || github.ref }}-${{github.event.review.state}}
  cancel-in-progress: true

jobs:
  get-metadata:
    name: Get Metadata
    runs-on: ubuntu-latest
    if: github.event.review.state == 'approved'
    steps:
      - name: Check out current commit
        uses: actions/checkout@b4ffde65f46336ab88eb53be808477a3936bae11 # v4.1.1
        with:
          ref: ${{ github.event.pull_request.head.sha }}
          fetch-depth: 2

      - name: Determine changed files
        uses: tomi/paths-filter-action@32c62f5ca100c1110406e3477d5b3ecef4666fec # v3.0.2
        id: changed
        if: github.event_name == 'pull_request_review'
        with:
          filters: |
            design_system:
              - packages/design-system/**
              - .github/workflows/chromatic.yml

    outputs:
      design_system_files_changed: ${{ steps.changed.outputs.design_system == 'true' }}
      is_community_pr: ${{ contains(github.event.pull_request.labels.*.name, 'community') }}
      is_pr_target_master: ${{ github.event.pull_request.base.ref == 'master' }}
      is_dispatch: ${{ github.event_name == 'workflow_dispatch' }}
      is_pr_approved: ${{ github.event.review.state == 'approved' }}

  chromatic:
    needs: [get-metadata]
    if: |
      needs.get-metadata.outputs.is_dispatch == 'true' ||
      (
       needs.get-metadata.outputs.design_system_files_changed == 'true' &&
       needs.get-metadata.outputs.is_community_pr == 'false' &&
       needs.get-metadata.outputs.is_pr_target_master == 'true' &&
       needs.get-metadata.outputs.is_pr_approved == 'true'
      )
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@b4ffde65f46336ab88eb53be808477a3936bae11 # v4.1.1
        with:
          fetch-depth: 0

      - uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          node-version: 22.x

      - name: Setup corepack and pnpm
        run: |
          npm i -g corepack@0.33
          corepack enable

      - run: pnpm install --frozen-lockfile

      - name: Publish to Chromatic
        uses: chromaui/action@c93e0bc3a63aa176e14a75b61a31847cbfdd341c # v11
        id: chromatic_tests
        continue-on-error: true
        with:
          workingDir: packages/design-system
          onlyChanged: true
          projectToken: ${{ secrets.CHROMATIC_PROJECT_TOKEN }}
          exitZeroOnChanges: false

      - name: Success comment
        if: steps.chromatic_tests.outcome == 'success' && github.ref != 'refs/heads/master'
        uses: peter-evans/create-or-update-comment@71345be0265236311c031f5c7866368bd1eff043 # v4.0.0
        with:
          issue-number: ${{ github.event.pull_request.number }}
          token: ${{ secrets.GITHUB_TOKEN }}
          edit-mode: replace
          body: |
            :white_check_mark: No visual regressions found.

      - name: Fail comment
        if: steps.chromatic_tests.outcome != 'success' && github.ref != 'refs/heads/master'
        uses: peter-evans/create-or-update-comment@71345be0265236311c031f5c7866368bd1eff043 # v4.0.0
        with:
          issue-number: ${{ github.event.pull_request.number }}
          token: ${{ secrets.GITHUB_TOKEN }}
          edit-mode: replace
          body: |
            [:warning: Visual regressions found](${{steps.chromatic_tests.outputs.url}}): ${{steps.chromatic_tests.outputs.changeCount}}
