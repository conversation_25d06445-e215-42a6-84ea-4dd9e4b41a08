# n8n 科技新闻智能聚合器 - 现有实例配置指南

## 📋 概述

这是一个可以直接导入到现有n8n实例的自动化工作流，具备以下功能：
- ⏰ 每天早上10:00自动执行
- 📰 从TechCrunch、Wired、Hacker News、arXiv抓取最新科技资讯
- 🤖 使用Google Gemini AI进行智能分析和分类
- 📝 生成中文科技新闻日报
- 📧 自动发送到指定邮箱 (<EMAIL>)
- 💾 保存为本地Markdown文件

## 🏗️ 工作流架构

```
定时触发器 → 数据抓取(4个并行) → RSS解析 → AI分析 → 内容处理 → 格式化 → 邮件&文件输出 → 完成通知
```

### 节点说明：
1. **每日10点触发** - Schedule Trigger，每天10:00自动执行
2. **数据抓取** - 4个HTTP Request节点并行抓取RSS源
3. **RSS解析器** - Code节点解析XML数据并清洗
4. **Gemini内容分析** - HTTP Request调用Google Gemini API (已配置密钥)
5. **内容处理器** - Code节点处理AI响应并分类排序
6. **Markdown生成器** - Code节点生成格式化日报
7. **发送邮件** - Email Send节点发送到*******************
8. **保存文件** - 本地保存Markdown文件
9. **完成通知** - 执行状态日志记录

## 🚀 快速开始

### 第一步：安装依赖包

在您的n8n环境中安装必需的npm包：

```bash
# 方法1：如果您有n8n服务器访问权限
npm install xml2js

# 方法2：如果使用Docker部署的n8n
docker exec -it your-n8n-container npm install xml2js

# 方法3：如果使用n8n Cloud，依赖包通常已预装
```

### 第二步：导入工作流

1. **打开您的n8n界面**
   - 访问您的n8n实例 (通常是 http://localhost:5678 或您的域名)
   - 使用您的账户登录

2. **导入工作流文件**
   - 点击右上角的 "+" 按钮
   - 选择 "Import from file"
   - 上传 `tech-news-aggregator-workflow.json` 文件
   - 点击 "Import" 确认导入

3. **验证导入结果**
   - 确认所有节点都正确显示
   - 检查节点连接是否完整
   - 工作流名称应显示为 "科技新闻智能聚合器"

### 第三步：配置SMTP邮箱

由于工作流需要发送邮件到 <EMAIL>，您需要配置SMTP凭据：

#### Gmail SMTP配置 (推荐)

1. **在n8n中添加SMTP凭据**
   - 点击左侧菜单 "Credentials"
   - 点击 "Add Credential"
   - 搜索并选择 "SMTP"
   - 设置凭据名称为 `Gmail SMTP`

2. **配置SMTP参数**
   ```
   Host: smtp.gmail.com
   Port: 587
   Security: STARTTLS
   Username: 您的Gmail地址 (例如: <EMAIL>)
   Password: Gmail应用专用密码 (不是普通密码)
   ```

3. **获取Gmail应用专用密码**
   - 登录 [Google账户设置](https://myaccount.google.com/)
   - 进入 "安全性" → "两步验证"
   - 点击 "应用专用密码"
   - 选择 "邮件" 和 "其他设备"
   - 生成16位应用专用密码
   - 将此密码填入n8n的SMTP配置中

#### 其他邮箱SMTP设置
- **QQ邮箱**: smtp.qq.com:587 (需要开启SMTP服务)
- **163邮箱**: smtp.163.com:25
- **Outlook**: smtp-mail.outlook.com:587
- **企业邮箱**: 联系您的IT管理员获取SMTP设置

### 第四步：激活工作流

1. **关联SMTP凭据**
   - 双击 "发送邮件" 节点
   - 在 "Credential for SMTP" 下拉框中选择刚创建的 `Gmail SMTP`
   - 点击 "Save" 保存节点配置

2. **激活工作流**
   - 点击工作流右上角的 "Inactive" 开关
   - 开关变为 "Active" 绿色状态
   - 工作流现在会在每天10:00自动执行

### 第五步：测试工作流

#### 手动测试
1. **执行单次测试**
   - 点击 "每日10点触发" 节点
   - 点击 "Execute Node" 进行手动触发
   - 观察各个节点的执行状态

2. **检查执行结果**
   - 查看每个节点是否成功执行（绿色勾号）
   - 检查 "完成通知" 节点的输出日志
   - 确认邮件是否发送到 <EMAIL>
   - 检查是否生成了本地Markdown文件

#### 查看执行历史
- 点击左侧菜单 "Executions"
- 查看工作流的执行记录
- 点击具体执行记录查看详细日志

## ⚙️ 自定义配置 (可选)

### 修改触发时间
如需更改执行时间，编辑 "每日10点触发" 节点：
```json
"triggerAtHour": 10,    // 小时 (0-23)
"triggerAtMinute": 0    // 分钟 (0-59)
```

### 修改邮件接收者
如需更改收件人，编辑 "发送邮件" 节点：
```json
"toEmail": "<EMAIL>"
```

### 添加更多数据源
可以复制现有的HTTP Request节点，修改URL为其他RSS源：
- MIT Technology Review: https://www.technologyreview.com/feed/
- VentureBeat: https://venturebeat.com/feed/
- The Verge: https://www.theverge.com/rss/index.xml

## 🔍 故障排除

### 常见问题及解决方案

#### 1. xml2js模块未找到
**错误信息**: `Cannot find module 'xml2js'`
**解决方案**:
```bash
# 在n8n环境中安装依赖
npm install xml2js

# 或者在Docker容器中安装
docker exec -it your-n8n-container npm install xml2js
```

#### 2. RSS抓取失败
**症状**: HTTP Request节点返回错误或超时
**解决方案**:
- 检查网络连接是否正常
- 验证RSS URL是否可访问
- 某些网站可能需要User-Agent头，可在HTTP Request节点中添加
- 增加超时时间到60秒

#### 3. Gemini API调用失败
**症状**: "Gemini内容分析" 节点报错
**解决方案**:
- 验证API密钥是否正确 (AIzaSyCdXtkWSUFA9cmcFXlFYDHwsqWoZaNi8WE)
- 检查API配额是否用完
- 确认网络可以访问Google API
- 查看节点输出的具体错误信息

#### 4. 邮件发送失败
**症状**: "发送邮件" 节点执行失败
**解决方案**:
- 检查SMTP凭据配置是否正确
- 确认使用的是Gmail应用专用密码，不是普通密码
- 验证Gmail账户是否开启了两步验证
- 检查防火墙是否阻止了SMTP连接

#### 5. 定时任务不执行
**症状**: 工作流没有按时自动触发
**解决方案**:
- 确认工作流状态为 "Active"
- 检查服务器时区设置是否正确
- 查看n8n的执行历史记录
- 重新保存并激活工作流

## 📊 工作流输出示例

### 邮件内容格式
您将收到如下格式的邮件：

**主题**: 🗞️ 科技新闻日报 - 2025-01-12 (15篇文章)

**内容**: Markdown格式的新闻日报，包含：
- 📊 今日概览统计
- 🤖 人工智能/机器学习类新闻
- 💻 科技产品/硬件类新闻
- 👨‍💻 软件开发/编程类新闻
- 💼 创业/商业类新闻
- 📚 学术研究类新闻
- 📰 其他科技新闻

### 本地文件
同时会在n8n工作目录下生成文件：
- 文件名: `科技新闻日报-2025-01-12.md`
- 格式: Markdown
- 内容: 与邮件内容相同

## 📈 监控和维护

### 执行状态监控
1. **查看执行历史**
   - 在n8n界面点击 "Executions"
   - 查看每日的执行记录
   - 绿色表示成功，红色表示失败

2. **日志查看**
   - 点击具体的执行记录
   - 查看每个节点的输出和错误信息
   - "完成通知" 节点会显示处理统计

### 定期维护建议
- **每周检查**: 确认邮件正常接收
- **每月检查**: 查看执行历史，确认无持续性错误
- **API配额监控**: 关注Gemini API的使用量
- **文件清理**: 定期清理旧的日报文件

## 🔧 技术支持

### 调试技巧
1. **单步测试**: 手动执行每个节点，检查输出
2. **日志分析**: 查看Console输出和错误信息
3. **数据检查**: 验证每个节点的输入输出数据格式

### 获取帮助
- **n8n官方文档**: https://docs.n8n.io/
- **社区论坛**: https://community.n8n.io/
- **GitHub Issues**: https://github.com/n8n-io/n8n/issues

## 📄 配置摘要

### 已预配置项目 ✅
- ✅ Google Gemini API密钥 (AIzaSyCdXtkWSUFA9cmcFXlFYDHwsqWoZaNi8WE)
- ✅ 收件邮箱 (<EMAIL>)
- ✅ 数据源 (TechCrunch, Wired, Hacker News, arXiv)
- ✅ 定时触发 (每天10:00)
- ✅ 错误处理和重试机制
- ✅ 中文AI分析和分类
- ✅ Markdown格式输出

### 需要您配置的项目 ⚠️
- ⚠️ 安装xml2js依赖包
- ⚠️ 配置SMTP邮箱凭据
- ⚠️ 导入并激活工作流

---

**最后更新**: 2025年1月12日
**版本**: v2.0.0 (现有实例专用版)
**状态**: 生产就绪

## 🔍 故障排除

### 常见问题及解决方案

#### 1. RSS抓取失败
**症状**: HTTP Request节点返回错误
**解决方案**:
- 检查网络连接
- 验证RSS URL是否有效
- 增加超时时间
- 检查是否需要User-Agent头

#### 2. Gemini API调用失败
**症状**: AI分析节点报错
**解决方案**:
- 验证API密钥是否正确
- 检查API配额是否用完
- 确认请求格式是否正确
- 查看API文档更新

#### 3. 邮件发送失败
**症状**: 邮件节点执行失败
**解决方案**:
- 检查SMTP配置
- 验证邮箱密码（使用应用专用密码）
- 确认防火墙设置
- 检查邮件服务商限制

#### 4. 定时任务不执行
**症状**: 工作流没有按时触发
**解决方案**:
- 确认工作流已激活
- 检查服务器时区设置
- 验证cron表达式
- 查看n8n日志

### 调试技巧

#### 1. 启用详细日志
```bash
# 设置环境变量
export N8N_LOG_LEVEL=debug
n8n start
```

#### 2. 单步测试
- 使用手动触发器测试各个节点
- 检查每个节点的输出数据
- 验证数据流转是否正确

#### 3. 监控执行历史
- 在n8n界面查看执行历史
- 分析失败的执行记录
- 检查错误消息和堆栈跟踪

## 📊 使用统计和优化

### 性能指标监控
建议监控以下指标：
- 工作流执行时间
- 抓取到的文章数量
- AI分析成功率
- 邮件发送成功率

### 成本优化
- **Gemini API**: 监控token使用量，优化提示词长度
- **邮件服务**: 考虑使用免费SMTP服务或企业邮箱
- **存储**: 定期清理旧的日报文件

## 🤝 贡献和支持

### 改进建议
欢迎提出以下改进：
- 新的数据源集成
- AI分析算法优化
- 输出格式增强
- 错误处理改进

### 技术支持
如遇到问题，可以：
1. 查看n8n官方文档
2. 检查工作流执行日志
3. 参考本配置指南
4. 联系技术支持团队

## 📄 许可证

本工作流配置遵循MIT许可证，可自由使用和修改。

---

**最后更新**: 2025年1月12日
**版本**: v1.0.0
**作者**: AI助手
