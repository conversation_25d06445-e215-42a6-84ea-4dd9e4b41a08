# n8n 科技新闻智能聚合器配置指南

## 📋 概述

这是一个基于n8n的自动化工作流，能够：
- 每天早上10:00自动执行
- 从TechCrunch、Wired、Hacker News、arXiv抓取最新科技资讯
- 使用Google Gemini AI进行智能分析和分类
- 生成中文科技新闻日报
- 通过邮件、Slack和本地文件多种方式输出

## 🏗️ 工作流架构

```
定时触发器 → 数据抓取(4个并行) → RSS解析 → AI分析 → 内容处理 → 格式化 → 输出(3个并行)
```

### 节点说明：
1. **每日10点触发** - Schedule Trigger节点，每天10:00执行
2. **数据抓取节点** - 4个HTTP Request节点并行抓取不同数据源
3. **RSS解析器** - Code节点解析RSS/XML数据
4. **Gemini内容分析** - HTTP Request节点调用Google Gemini API
5. **内容处理器** - Code节点处理AI响应并排序
6. **Markdown生成器** - Code节点生成最终报告
7. **输出节点** - 邮件发送、文件保存、Slack通知

## 🔧 环境要求

### 系统要求
- n8n版本 >= 1.0.0
- Node.js >= 18.0.0
- 支持定时任务的环境

### 依赖包
工作流中的Code节点需要以下npm包：
```bash
# 在n8n环境中安装
npm install xml2js
```

## 🔑 API密钥配置

### 1. Google Gemini API配置

#### 获取API密钥：
1. 访问 [Google AI Studio](https://makersuite.google.com/app/apikey)
2. 创建新的API密钥
3. 复制密钥备用

#### 在n8n中配置：
1. 进入n8n管理界面
2. 点击 "Credentials" → "Add Credential"
3. 选择 "HTTP Header Auth"
4. 配置如下：
   - **Name**: `Google Gemini API`
   - **Header Name**: `x-goog-api-key`
   - **Header Value**: `你的Gemini API密钥`

### 2. SMTP邮箱配置

#### 配置邮件发送：
1. 在n8n中添加SMTP凭据
2. 点击 "Credentials" → "Add Credential"
3. 选择 "SMTP"
4. 配置参数：
   ```
   Host: smtp.gmail.com (Gmail示例)
   Port: 587
   Security: STARTTLS
   Username: 你的邮箱地址
   Password: 应用专用密码
   ```

#### 常用邮箱SMTP设置：
- **Gmail**: smtp.gmail.com:587 (需要应用专用密码)
- **QQ邮箱**: smtp.qq.com:587
- **163邮箱**: smtp.163.com:25
- **企业邮箱**: 联系管理员获取SMTP设置

### 3. Slack Webhook配置

#### 创建Slack Webhook：
1. 访问 [Slack API](https://api.slack.com/apps)
2. 创建新应用或选择现有应用
3. 启用 "Incoming Webhooks"
4. 创建新的Webhook URL
5. 复制Webhook URL

#### 在工作流中配置：
修改 "Slack通知" 节点的URL参数：
```json
"url": "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK"
```

## 📝 工作流导入步骤

### 1. 导入工作流
1. 打开n8n界面
2. 点击 "Import from file" 或 "Import from URL"
3. 选择 `tech-news-aggregator-workflow.json` 文件
4. 点击导入

### 2. 配置凭据
按照上述API密钥配置步骤，为以下节点配置凭据：
- **Gemini内容分析** 节点 → Google Gemini API凭据
- **发送邮件** 节点 → SMTP凭据

### 3. 自定义配置

#### 修改邮件接收者：
在 "发送邮件" 节点中修改：
```json
"fromEmail": "<EMAIL>",
"toEmail": "<EMAIL>"
```

#### 修改文件保存路径：
在 "保存文件" 节点中可以修改保存路径和文件名格式。

#### 调整触发时间：
在 "每日10点触发" 节点中修改：
```json
"triggerAtHour": 10,  // 小时 (0-23)
"triggerAtMinute": 0  // 分钟 (0-59)
```

## 🚀 部署和运行

### 1. 本地部署
```bash
# 安装n8n
npm install -g n8n

# 启动n8n
n8n start

# 访问 http://localhost:5678
```

### 2. Docker部署
```bash
# 创建docker-compose.yml
version: '3.8'
services:
  n8n:
    image: n8nio/n8n:latest
    ports:
      - "5678:5678"
    environment:
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=admin
      - N8N_BASIC_AUTH_PASSWORD=your_password
    volumes:
      - n8n_data:/home/<USER>/.n8n
volumes:
  n8n_data:

# 启动服务
docker-compose up -d
```

### 3. 云服务部署
推荐使用以下云服务：
- **n8n Cloud** - 官方托管服务
- **Railway** - 支持一键部署
- **Heroku** - 免费层可用
- **DigitalOcean** - VPS部署

## ⚙️ 高级配置

### 1. 自定义数据源

#### 添加新的RSS源：
1. 复制现有的HTTP Request节点
2. 修改URL为新的RSS地址
3. 在RSS解析器中添加对应的source标识
4. 连接到RSS解析器节点

#### 支持的RSS格式：
- RSS 2.0
- Atom 1.0
- RSS 1.0

### 2. AI分析优化

#### 调整Gemini提示词：
在 "Gemini内容分析" 节点中可以修改提示词来优化分析效果：

```javascript
// 自定义分析提示
const customPrompt = `
请分析以下科技新闻，重点关注：
1. 技术创新程度
2. 商业影响力
3. 行业趋势意义
4. 对普通用户的影响

分类标准：
- 人工智能/机器学习：AI、ML、深度学习相关
- 科技产品/硬件：新产品发布、硬件创新
- 软件开发/编程：开发工具、编程语言、框架
- 创业/商业：融资、IPO、商业模式
- 学术研究：论文、研究成果
- 其他科技新闻：其他科技相关内容

请返回JSON格式...
`;
```

#### 替代AI服务：
如果需要使用其他AI服务，可以修改相应节点：
- **OpenAI GPT**: 修改API端点和认证方式
- **Claude**: 使用Anthropic API
- **本地模型**: 使用Ollama等本地部署方案

### 3. 错误处理和监控

#### 工作流已包含的错误处理：
- HTTP请求超时设置（30秒）
- 自动重试机制（最多3次）
- 节点级别的 `continueOnFail` 设置
- 异常捕获和日志记录

#### 添加监控告警：
```javascript
// 在Code节点中添加监控逻辑
if (processedItems.length === 0) {
  // 发送告警邮件或Slack消息
  console.error('警告：没有处理到任何新闻内容');
}
```

### 4. 性能优化

#### 并发控制：
- 数据抓取节点已配置为并行执行
- AI分析可能需要限制并发以避免API限制

#### 缓存策略：
```javascript
// 在RSS解析器中添加缓存逻辑
const staticData = this.getWorkflowStaticData('node');
const lastProcessed = staticData.lastProcessed || {};

// 避免重复处理相同内容
if (lastProcessed[item.link]) {
  continue;
}
```

## 🔍 故障排除

### 常见问题及解决方案

#### 1. RSS抓取失败
**症状**: HTTP Request节点返回错误
**解决方案**:
- 检查网络连接
- 验证RSS URL是否有效
- 增加超时时间
- 检查是否需要User-Agent头

#### 2. Gemini API调用失败
**症状**: AI分析节点报错
**解决方案**:
- 验证API密钥是否正确
- 检查API配额是否用完
- 确认请求格式是否正确
- 查看API文档更新

#### 3. 邮件发送失败
**症状**: 邮件节点执行失败
**解决方案**:
- 检查SMTP配置
- 验证邮箱密码（使用应用专用密码）
- 确认防火墙设置
- 检查邮件服务商限制

#### 4. 定时任务不执行
**症状**: 工作流没有按时触发
**解决方案**:
- 确认工作流已激活
- 检查服务器时区设置
- 验证cron表达式
- 查看n8n日志

### 调试技巧

#### 1. 启用详细日志
```bash
# 设置环境变量
export N8N_LOG_LEVEL=debug
n8n start
```

#### 2. 单步测试
- 使用手动触发器测试各个节点
- 检查每个节点的输出数据
- 验证数据流转是否正确

#### 3. 监控执行历史
- 在n8n界面查看执行历史
- 分析失败的执行记录
- 检查错误消息和堆栈跟踪

## 📊 使用统计和优化

### 性能指标监控
建议监控以下指标：
- 工作流执行时间
- 抓取到的文章数量
- AI分析成功率
- 邮件发送成功率

### 成本优化
- **Gemini API**: 监控token使用量，优化提示词长度
- **邮件服务**: 考虑使用免费SMTP服务或企业邮箱
- **存储**: 定期清理旧的日报文件

## 🤝 贡献和支持

### 改进建议
欢迎提出以下改进：
- 新的数据源集成
- AI分析算法优化
- 输出格式增强
- 错误处理改进

### 技术支持
如遇到问题，可以：
1. 查看n8n官方文档
2. 检查工作流执行日志
3. 参考本配置指南
4. 联系技术支持团队

## 📄 许可证

本工作流配置遵循MIT许可证，可自由使用和修改。

---

**最后更新**: 2025年1月12日
**版本**: v1.0.0
**作者**: AI助手
