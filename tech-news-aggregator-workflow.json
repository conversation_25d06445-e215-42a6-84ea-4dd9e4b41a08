{"name": "科技新闻智能聚合器", "nodes": [{"parameters": {"rule": {"interval": [{"field": "hours", "hoursInterval": 24, "triggerAtHour": 10, "triggerAtMinute": 0}]}}, "id": "schedule-trigger-001", "name": "每日10点触发", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [240, 300]}, {"parameters": {"method": "GET", "url": "https://techcrunch.com/feed/", "options": {"timeout": 30000, "retry": {"enabled": true, "maxRetries": 3}}}, "id": "techcrunch-fetch-001", "name": "抓取TechCrunch", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [460, 200], "continueOnFail": true}, {"parameters": {"method": "GET", "url": "https://www.wired.com/feed/rss", "options": {"timeout": 30000, "retry": {"enabled": true, "maxRetries": 3}}}, "id": "wired-fetch-001", "name": "抓取Wired", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [460, 300], "continueOnFail": true}, {"parameters": {"method": "GET", "url": "https://hnrss.org/frontpage", "options": {"timeout": 30000, "retry": {"enabled": true, "maxRetries": 3}}}, "id": "hackernews-fetch-001", "name": "抓取Hacker News", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [460, 400], "continueOnFail": true}, {"parameters": {"method": "GET", "url": "http://export.arxiv.org/rss/cs", "options": {"timeout": 30000, "retry": {"enabled": true, "maxRetries": 3}}}, "id": "arxiv-fetch-001", "name": "抓取arXiv", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [460, 500], "continueOnFail": true}, {"parameters": {"jsCode": "// RSS解析和数据清洗\nconst xml2js = require('xml2js');\nconst parser = new xml2js.Parser();\n\nconst items = [];\nconst now = new Date();\nconst yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);\n\nfor (const inputData of $input.all()) {\n  try {\n    // 确定数据源\n    let source = '未知来源';\n    const nodeId = inputData.json.$node?.name || '';\n    if (nodeId.includes('TechCrunch')) source = 'TechCrunch';\n    else if (nodeId.includes('Wired')) source = 'Wired';\n    else if (nodeId.includes('Hacker')) source = 'Hacker News';\n    else if (nodeId.includes('arXiv')) source = 'arXiv';\n    \n    const xmlData = inputData.json.data || inputData.json.body || inputData.json;\n    \n    if (!xmlData || typeof xmlData !== 'string') continue;\n    \n    const result = await parser.parseStringPromise(xmlData);\n    const rssItems = result.rss?.channel?.[0]?.item || result.feed?.entry || [];\n    \n    for (const item of rssItems.slice(0, 10)) { // 限制每个源最多10篇文章\n      const pubDate = new Date(item.pubDate?.[0] || item.published?.[0] || item.updated?.[0] || now);\n      \n      // 只处理24小时内的文章\n      if (pubDate >= yesterday) {\n        items.push({\n          title: (item.title?.[0] || item.title?._ || '无标题').replace(/<[^>]*>/g, ''),\n          link: item.link?.[0] || item.link?.$.href || item.id?.[0] || '',\n          description: (item.description?.[0] || item.summary?.[0] || '').replace(/<[^>]*>/g, '').substring(0, 500),\n          pubDate: pubDate.toISOString(),\n          source: source,\n          content: (item['content:encoded']?.[0] || item.content?.[0] || '').replace(/<[^>]*>/g, '').substring(0, 1000)\n        });\n      }\n    }\n  } catch (error) {\n    console.log(`解析RSS失败: ${error.message}`);\n  }\n}\n\nconsole.log(`共解析到 ${items.length} 篇文章`);\nreturn items.map(item => ({ json: item }));"}, "id": "rss-parser-001", "name": "RSS解析器", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [680, 350]}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "x-goog-api-key", "value": "AIzaSyCdXtkWSUFA9cmcFXlFYDHwsqWoZaNi8WE"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify({\n  \"contents\": [{\n    \"parts\": [{\n      \"text\": \"请分析以下科技新闻内容，并按照以下要求进行处理：\\n\\n1. 将内容分类到以下类别之一：人工智能/机器学习、科技产品/硬件、软件开发/编程、创业/商业、学术研究、其他科技新闻\\n2. 提供100-200字的中文总结，用通俗易懂的语言解释技术概念\\n3. 给出1-10的重要性评分\\n4. 返回JSON格式：{\\\"category\\\": \\\"分类\\\", \\\"summary\\\": \\\"总结\\\", \\\"importance\\\": 评分}\\n\\n新闻内容：\\n标题：\" + $json.title + \"\\n描述：\" + $json.description + \"\\n来源：\" + $json.source\n    }]\n  }]\n}) }}", "options": {"timeout": 60000}}, "id": "gemini-analysis-001", "name": "Gemini内容分析", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [900, 350]}, {"parameters": {"jsCode": "// 处理Gemini API响应并格式化输出\nconst processedItems = [];\n\nfor (const item of $input.all()) {\n  try {\n    // 获取原始新闻数据和Gemini分析结果\n    const newsData = item.json;\n    const geminiResponse = newsData.candidates?.[0]?.content?.parts?.[0]?.text;\n    \n    if (!geminiResponse) {\n      console.log('Gemini响应为空，使用默认分析');\n      processedItems.push({\n        title: newsData.title || '无标题',\n        category: '其他科技新闻',\n        summary: (newsData.description || '暂无描述').substring(0, 200),\n        importance: 5,\n        link: newsData.link || '',\n        source: newsData.source || '未知来源',\n        pubDate: newsData.pubDate || new Date().toISOString()\n      });\n      continue;\n    }\n    \n    // 尝试解析Gemini返回的JSON\n    let analysis = {\n      category: '其他科技新闻',\n      summary: '暂无总结',\n      importance: 5\n    };\n    \n    try {\n      // 查找JSON格式的响应\n      const jsonMatch = geminiResponse.match(/\\{[^{}]*\"category\"[^{}]*\\}/g);\n      if (jsonMatch && jsonMatch[0]) {\n        const parsed = JSON.parse(jsonMatch[0]);\n        analysis = {\n          category: parsed.category || '其他科技新闻',\n          summary: parsed.summary || geminiResponse.substring(0, 200),\n          importance: Math.min(Math.max(parseInt(parsed.importance) || 5, 1), 10)\n        };\n      } else {\n        // 如果没有JSON格式，从文本中提取信息\n        analysis.summary = geminiResponse.substring(0, 200);\n        \n        // 简单的关键词分类\n        const text = geminiResponse.toLowerCase();\n        if (text.includes('ai') || text.includes('人工智能') || text.includes('机器学习')) {\n          analysis.category = '人工智能/机器学习';\n          analysis.importance = 8;\n        } else if (text.includes('产品') || text.includes('硬件') || text.includes('设备')) {\n          analysis.category = '科技产品/硬件';\n          analysis.importance = 7;\n        } else if (text.includes('编程') || text.includes('开发') || text.includes('软件')) {\n          analysis.category = '软件开发/编程';\n          analysis.importance = 6;\n        } else if (text.includes('创业') || text.includes('融资') || text.includes('商业')) {\n          analysis.category = '创业/商业';\n          analysis.importance = 7;\n        } else if (text.includes('研究') || text.includes('论文') || text.includes('学术')) {\n          analysis.category = '学术研究';\n          analysis.importance = 6;\n        }\n      }\n    } catch (parseError) {\n      console.log(`解析Gemini响应失败: ${parseError.message}`);\n      analysis.summary = geminiResponse.substring(0, 200);\n    }\n    \n    processedItems.push({\n      title: newsData.title || '无标题',\n      category: analysis.category,\n      summary: analysis.summary,\n      importance: analysis.importance,\n      link: newsData.link || '',\n      source: newsData.source || '未知来源',\n      pubDate: newsData.pubDate || new Date().toISOString()\n    });\n    \n  } catch (error) {\n    console.log(`处理项目时出错: ${error.message}`);\n  }\n}\n\n// 按重要性和时间排序\nprocessedItems.sort((a, b) => {\n  if (b.importance !== a.importance) {\n    return b.importance - a.importance;\n  }\n  return new Date(b.pubDate) - new Date(a.pubDate);\n});\n\nconsole.log(`处理完成，共 ${processedItems.length} 篇文章`);\nreturn processedItems.map(item => ({ json: item }));"}, "id": "content-processor-001", "name": "内容处理器", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 350]}, {"parameters": {"jsCode": "// 生成Markdown格式的日报\nconst items = $input.all().map(item => item.json);\nconst today = new Date().toLocaleDateString('zh-CN', {\n  year: 'numeric',\n  month: '2-digit',\n  day: '2-digit'\n}).replace(/\\//g, '-');\n\nif (items.length === 0) {\n  return [{ json: { \n    markdown: `# 科技新闻日报 - ${today}\\n\\n> 今日暂无新闻内容\\n`,\n    itemCount: 0,\n    date: today\n  }}];\n}\n\n// 按类别分组\nconst categories = {\n  '人工智能/机器学习': [],\n  '科技产品/硬件': [],\n  '软件开发/编程': [],\n  '创业/商业': [],\n  '学术研究': [],\n  '其他科技新闻': []\n};\n\nitems.forEach(item => {\n  const category = item.category || '其他科技新闻';\n  if (categories[category]) {\n    categories[category].push(item);\n  } else {\n    categories['其他科技新闻'].push(item);\n  }\n});\n\n// 生成Markdown内容\nlet markdown = `# 🗞️ 科技新闻日报 - ${today}\\n\\n`;\nmarkdown += `> 本日报由AI自动生成，汇总了TechCrunch、Wired、Hacker News、arXiv等平台的最新科技资讯\\n\\n`;\nmarkdown += `## 📊 今日概览\\n\\n`;\nmarkdown += `- **总计文章数**: ${items.length}\\n`;\nmarkdown += `- **高重要性文章** (评分≥8): ${items.filter(item => item.importance >= 8).length}\\n`;\nmarkdown += `- **中等重要性文章** (评分5-7): ${items.filter(item => item.importance >= 5 && item.importance < 8).length}\\n`;\nmarkdown += `- **一般文章** (评分<5): ${items.filter(item => item.importance < 5).length}\\n\\n`;\n\n// 为每个类别生成内容\nObject.entries(categories).forEach(([category, categoryItems]) => {\n  if (categoryItems.length > 0) {\n    markdown += `## ${getCategoryIcon(category)} ${category} (${categoryItems.length}篇)\\n\\n`;\n    \n    categoryItems.slice(0, 8).forEach((item, index) => { // 每个类别最多显示8篇\n      const importanceStars = '⭐'.repeat(Math.min(Math.max(item.importance, 1), 5));\n      const publishTime = new Date(item.pubDate).toLocaleString('zh-CN');\n      \n      markdown += `### ${index + 1}. ${item.title}\\n\\n`;\n      markdown += `**📈 重要性**: ${importanceStars} (${item.importance}/10) | **📅 发布时间**: ${publishTime}\\n\\n`;\n      markdown += `**🏷️ 来源**: ${item.source}\\n\\n`;\n      markdown += `**📝 AI总结**: ${item.summary}\\n\\n`;\n      if (item.link) {\n        markdown += `**🔗 原文链接**: [点击查看详情](${item.link})\\n\\n`;\n      }\n      markdown += `---\\n\\n`;\n    });\n    \n    if (categoryItems.length > 8) {\n      markdown += `*还有 ${categoryItems.length - 8} 篇文章未显示...*\\n\\n`;\n    }\n  }\n});\n\nmarkdown += `## � 生成信息\\n\\n`;\nmarkdown += `- **生成时间**: ${new Date().toLocaleString('zh-CN')}\\n`;\nmarkdown += `- **数据来源**: TechCrunch, Wired, Hacker News, arXiv\\n`;\nmarkdown += `- **AI分析**: Google Gemini Pro\\n`;\nmarkdown += `- **工作流**: n8n自动化处理\\n\\n`;\nmarkdown += `---\\n*本报告由AI自动生成，如有疑问请联系管理员*`;\n\nfunction getCategoryIcon(category) {\n  const icons = {\n    '人工智能/机器学习': '🤖',\n    '科技产品/硬件': '💻',\n    '软件开发/编程': '👨‍💻',\n    '创业/商业': '💼',\n    '学术研究': '📚',\n    '其他科技新闻': '📰'\n  };\n  return icons[category] || '📰';\n}\n\nconsole.log(`生成日报完成，共 ${items.length} 篇文章`);\nreturn [{ json: { markdown, itemCount: items.length, date: today, summary: `共处理${items.length}篇文章` } }];"}, "id": "markdown-generator-001", "name": "Markdown生成器", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1340, 350]}, {"parameters": {"fromEmail": "<EMAIL>", "toEmail": "<EMAIL>", "subject": "=🗞️ 科技新闻日报 - {{ $json.date }} ({{ $json.itemCount }}篇文章)", "text": "={{ $json.markdown }}", "options": {"replyTo": "<EMAIL>"}}, "id": "email-sender-001", "name": "发送邮件", "type": "n8n-nodes-base.emailSend", "typeVersion": 2.1, "position": [1560, 250], "credentials": {"smtp": {"id": "gmail-smtp", "name": "Gmail SMTP"}}}, {"parameters": {"operation": "write", "fileName": "=科技新闻日报-{{ $json.date }}.md", "data": "={{ $json.markdown }}", "options": {"encoding": "utf8"}}, "id": "file-writer-001", "name": "保存文件", "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [1560, 450]}, {"parameters": {"jsCode": "// 发送成功通知\nconst data = $input.first().json;\n\nconsole.log(`✅ 科技新闻日报处理完成！`);\nconsole.log(`📅 日期: ${data.date}`);\nconsole.log(`📊 文章总数: ${data.itemCount}`);\nconsole.log(`� 邮件已发送至: <EMAIL>`);\nconsole.log(`💾 文件已保存: 科技新闻日报-${data.date}.md`);\n\nreturn [{\n  json: {\n    status: 'success',\n    message: `科技新闻日报处理完成，共处理${data.itemCount}篇文章`,\n    date: data.date,\n    itemCount: data.itemCount,\n    timestamp: new Date().toISOString()\n  }\n}];"}, "id": "success-notification-001", "name": "完成通知", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1780, 350]}], "connections": {"每日10点触发": {"main": [[{"node": "抓取TechCrunch", "type": "main", "index": 0}, {"node": "抓取Wired", "type": "main", "index": 0}, {"node": "抓取Hacker News", "type": "main", "index": 0}, {"node": "抓取arXiv", "type": "main", "index": 0}]]}, "抓取TechCrunch": {"main": [[{"node": "RSS解析器", "type": "main", "index": 0}]]}, "抓取Wired": {"main": [[{"node": "RSS解析器", "type": "main", "index": 0}]]}, "抓取Hacker News": {"main": [[{"node": "RSS解析器", "type": "main", "index": 0}]]}, "抓取arXiv": {"main": [[{"node": "RSS解析器", "type": "main", "index": 0}]]}, "RSS解析器": {"main": [[{"node": "Gemini内容分析", "type": "main", "index": 0}]]}, "Gemini内容分析": {"main": [[{"node": "内容处理器", "type": "main", "index": 0}]]}, "内容处理器": {"main": [[{"node": "Markdown生成器", "type": "main", "index": 0}]]}, "Markdown生成器": {"main": [[{"node": "发送邮件", "type": "main", "index": 0}, {"node": "保存文件", "type": "main", "index": 0}]]}, "发送邮件": {"main": [[{"node": "完成通知", "type": "main", "index": 0}]]}, "保存文件": {"main": [[{"node": "完成通知", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1", "saveManualExecutions": true, "callerPolicy": "workflowsFromSameOwner", "errorWorkflow": {"enabled": false}}, "staticData": {}, "tags": [{"createdAt": "2025-01-12T10:00:00.000Z", "updatedAt": "2025-01-12T10:00:00.000Z", "id": "tech-news", "name": "科技新闻"}], "triggerCount": 0, "updatedAt": "2025-01-12T10:00:00.000Z", "versionId": "1"}