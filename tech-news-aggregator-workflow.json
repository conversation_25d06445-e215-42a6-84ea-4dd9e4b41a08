{"name": "科技新闻智能聚合器", "nodes": [{"parameters": {"rule": {"interval": [{"field": "hours", "hoursInterval": 24, "triggerAtHour": 10, "triggerAtMinute": 0}]}}, "id": "schedule-trigger-001", "name": "每日10点触发", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [240, 300]}, {"parameters": {"method": "GET", "url": "https://techcrunch.com/feed/", "options": {"timeout": 30000, "retry": {"enabled": true, "maxRetries": 3}}}, "id": "techcrunch-fetch-001", "name": "抓取TechCrunch", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [460, 200], "continueOnFail": true}, {"parameters": {"method": "GET", "url": "https://www.wired.com/feed/rss", "options": {"timeout": 30000, "retry": {"enabled": true, "maxRetries": 3}}}, "id": "wired-fetch-001", "name": "抓取Wired", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [460, 300], "continueOnFail": true}, {"parameters": {"method": "GET", "url": "https://hnrss.org/frontpage", "options": {"timeout": 30000, "retry": {"enabled": true, "maxRetries": 3}}}, "id": "hackernews-fetch-001", "name": "抓取Hacker News", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [460, 400], "continueOnFail": true}, {"parameters": {"method": "GET", "url": "http://export.arxiv.org/rss/cs", "options": {"timeout": 30000, "retry": {"enabled": true, "maxRetries": 3}}}, "id": "arxiv-fetch-001", "name": "抓取arXiv", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [460, 500], "continueOnFail": true}, {"parameters": {"jsCode": "// RSS解析和数据清洗\nconst xml2js = require('xml2js');\nconst parser = new xml2js.Parser();\n\nconst items = [];\nconst now = new Date();\nconst yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);\n\nfor (const inputData of $input.all()) {\n  try {\n    const source = inputData.json.source || '未知来源';\n    const xmlData = inputData.json.data || inputData.json.body;\n    \n    if (!xmlData) continue;\n    \n    const result = await parser.parseStringPromise(xmlData);\n    const rssItems = result.rss?.channel?.[0]?.item || result.feed?.entry || [];\n    \n    for (const item of rssItems) {\n      const pubDate = new Date(item.pubDate?.[0] || item.published?.[0] || item.updated?.[0] || now);\n      \n      // 只处理24小时内的文章\n      if (pubDate >= yesterday) {\n        items.push({\n          title: item.title?.[0] || item.title?._ || '无标题',\n          link: item.link?.[0] || item.link?.$.href || '',\n          description: item.description?.[0] || item.summary?.[0] || '',\n          pubDate: pubDate.toISOString(),\n          source: source,\n          content: item['content:encoded']?.[0] || item.content?.[0] || ''\n        });\n      }\n    }\n  } catch (error) {\n    console.log(`解析RSS失败: ${error.message}`);\n  }\n}\n\nreturn items.map(item => ({ json: item }));"}, "id": "rss-parser-001", "name": "RSS解析器", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [680, 350]}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "contents", "value": "={{ JSON.stringify([{\"parts\": [{\"text\": \"请分析以下科技新闻内容，并按照以下要求进行处理：\\n\\n1. 将内容分类到以下类别之一：人工智能/机器学习、科技产品/硬件、软件开发/编程、创业/商业、学术研究、其他科技新闻\\n2. 提供100-200字的中文总结，用通俗易懂的语言解释技术概念\\n3. 给出1-10的重要性评分\\n4. 返回JSON格式：{\\\"category\\\": \\\"分类\\\", \\\"summary\\\": \\\"总结\\\", \\\"importance\\\": 评分}\\n\\n新闻内容：\\n标题：\" + $json.title + \"\\n描述：\" + $json.description + \"\\n来源：\" + $json.source}]}]) }}"}]}, "options": {"timeout": 60000}}, "id": "gemini-analysis-001", "name": "Gemini内容分析", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [900, 350], "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "x-goog-api-key", "value": "AIzaSyCdXtkWSUFA9cmcFXlFYDHwsqWoZaNi8WE"}]}}, {"parameters": {"jsCode": "// 处理Gemini API响应并格式化输出\nconst processedItems = [];\n\nfor (const item of $input.all()) {\n  try {\n    const originalData = item.json;\n    const geminiResponse = originalData.candidates?.[0]?.content?.parts?.[0]?.text;\n    \n    if (!geminiResponse) {\n      console.log('Gemini响应为空，跳过此项');\n      continue;\n    }\n    \n    // 尝试解析Gemini返回的JSON\n    let analysis;\n    try {\n      const jsonMatch = geminiResponse.match(/\\{[^}]+\\}/);\n      if (jsonMatch) {\n        analysis = JSON.parse(jsonMatch[0]);\n      } else {\n        // 如果没有找到JSON，使用默认值\n        analysis = {\n          category: '其他科技新闻',\n          summary: geminiResponse.substring(0, 200),\n          importance: 5\n        };\n      }\n    } catch (parseError) {\n      analysis = {\n        category: '其他科技新闻',\n        summary: geminiResponse.substring(0, 200),\n        importance: 5\n      };\n    }\n    \n    processedItems.push({\n      title: originalData.title,\n      category: analysis.category || '其他科技新闻',\n      summary: analysis.summary || '暂无总结',\n      importance: analysis.importance || 5,\n      link: originalData.link,\n      source: originalData.source,\n      pubDate: originalData.pubDate\n    });\n    \n  } catch (error) {\n    console.log(`处理项目时出错: ${error.message}`);\n  }\n}\n\n// 按重要性排序\nprocessedItems.sort((a, b) => b.importance - a.importance);\n\nreturn processedItems.map(item => ({ json: item }));"}, "id": "content-processor-001", "name": "内容处理器", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 350]}, {"parameters": {"jsCode": "// 生成Markdown格式的日报\nconst items = $input.all().map(item => item.json);\nconst today = new Date().toLocaleDateString('zh-CN');\n\n// 按类别分组\nconst categories = {\n  '人工智能/机器学习': [],\n  '科技产品/硬件': [],\n  '软件开发/编程': [],\n  '创业/商业': [],\n  '学术研究': [],\n  '其他科技新闻': []\n};\n\nitems.forEach(item => {\n  const category = item.category || '其他科技新闻';\n  if (categories[category]) {\n    categories[category].push(item);\n  } else {\n    categories['其他科技新闻'].push(item);\n  }\n});\n\n// 生成Markdown内容\nlet markdown = `# 科技新闻日报 - ${today}\\n\\n`;\nmarkdown += `> 本日报由AI自动生成，汇总了TechCrunch、Wired、Hacker News、arXiv等平台的最新科技资讯\\n\\n`;\nmarkdown += `## 📊 今日概览\\n\\n`;\nmarkdown += `- 总计文章数：${items.length}\\n`;\nmarkdown += `- 高重要性文章（评分≥8）：${items.filter(item => item.importance >= 8).length}\\n`;\nmarkdown += `- 中等重要性文章（评分5-7）：${items.filter(item => item.importance >= 5 && item.importance < 8).length}\\n`;\nmarkdown += `- 一般文章（评分<5）：${items.filter(item => item.importance < 5).length}\\n\\n`;\n\n// 为每个类别生成内容\nObject.entries(categories).forEach(([category, categoryItems]) => {\n  if (categoryItems.length > 0) {\n    markdown += `## ${getCategoryIcon(category)} ${category}\\n\\n`;\n    \n    categoryItems.forEach((item, index) => {\n      const importanceStars = '⭐'.repeat(Math.min(item.importance, 5));\n      markdown += `### ${index + 1}. ${item.title}\\n\\n`;\n      markdown += `**重要性：** ${importanceStars} (${item.importance}/10)\\n\\n`;\n      markdown += `**来源：** ${item.source}\\n\\n`;\n      markdown += `**总结：** ${item.summary}\\n\\n`;\n      markdown += `**原文链接：** [查看详情](${item.link})\\n\\n`;\n      markdown += `---\\n\\n`;\n    });\n  }\n});\n\nmarkdown += `## 📝 生成信息\\n\\n`;\nmarkdown += `- 生成时间：${new Date().toLocaleString('zh-CN')}\\n`;\nmarkdown += `- 数据来源：TechCrunch, Wired, Hacker News, arXiv\\n`;\nmarkdown += `- AI分析：Google Gemini\\n`;\n\nfunction getCategoryIcon(category) {\n  const icons = {\n    '人工智能/机器学习': '🤖',\n    '科技产品/硬件': '💻',\n    '软件开发/编程': '👨‍💻',\n    '创业/商业': '💼',\n    '学术研究': '📚',\n    '其他科技新闻': '📰'\n  };\n  return icons[category] || '📰';\n}\n\nreturn [{ json: { markdown, itemCount: items.length, date: today } }];"}, "id": "markdown-generator-001", "name": "Markdown生成器", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1340, 350]}, {"parameters": {"fromEmail": "<EMAIL>", "toEmail": "<EMAIL>", "subject": "=科技新闻日报 - {{ $json.date }}", "text": "={{ $json.markdown }}", "options": {"replyTo": "<EMAIL>"}}, "id": "email-sender-001", "name": "发送邮件", "type": "n8n-nodes-base.emailSend", "typeVersion": 2.1, "position": [1560, 250], "credentials": {"smtp": {"id": "smtp-credentials", "name": "SMTP邮箱配置"}}}, {"parameters": {"operation": "write", "fileName": "=科技新闻日报-{{ $json.date }}.md", "data": "={{ $json.markdown }}", "options": {"encoding": "utf8"}}, "id": "file-writer-001", "name": "保存文件", "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [1560, 450]}, {"parameters": {"method": "POST", "url": "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK", "sendBody": true, "bodyParameters": {"parameters": [{"name": "text", "value": "=📰 科技新闻日报已生成！\\n\\n📅 日期：{{ $json.date }}\\n📊 文章总数：{{ $json.itemCount }}\\n\\n点击查看详细内容 👆"}, {"name": "username", "value": "科技新闻机器人"}, {"name": "icon_emoji", "value": ":newspaper:"}]}, "options": {"timeout": 30000}}, "id": "slack-notification-001", "name": "Slack通知", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1560, 350]}], "connections": {"每日10点触发": {"main": [[{"node": "抓取TechCrunch", "type": "main", "index": 0}, {"node": "抓取Wired", "type": "main", "index": 0}, {"node": "抓取Hacker News", "type": "main", "index": 0}, {"node": "抓取arXiv", "type": "main", "index": 0}]]}, "抓取TechCrunch": {"main": [[{"node": "RSS解析器", "type": "main", "index": 0}]]}, "抓取Wired": {"main": [[{"node": "RSS解析器", "type": "main", "index": 0}]]}, "抓取Hacker News": {"main": [[{"node": "RSS解析器", "type": "main", "index": 0}]]}, "抓取arXiv": {"main": [[{"node": "RSS解析器", "type": "main", "index": 0}]]}, "RSS解析器": {"main": [[{"node": "Gemini内容分析", "type": "main", "index": 0}]]}, "Gemini内容分析": {"main": [[{"node": "内容处理器", "type": "main", "index": 0}]]}, "内容处理器": {"main": [[{"node": "Markdown生成器", "type": "main", "index": 0}]]}, "Markdown生成器": {"main": [[{"node": "发送邮件", "type": "main", "index": 0}, {"node": "保存文件", "type": "main", "index": 0}, {"node": "Slack通知", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1", "saveManualExecutions": true, "callerPolicy": "workflowsFromSameOwner", "errorWorkflow": {"enabled": false}}, "staticData": {}, "tags": [{"createdAt": "2025-01-12T10:00:00.000Z", "updatedAt": "2025-01-12T10:00:00.000Z", "id": "tech-news", "name": "科技新闻"}], "triggerCount": 0, "updatedAt": "2025-01-12T10:00:00.000Z", "versionId": "1"}