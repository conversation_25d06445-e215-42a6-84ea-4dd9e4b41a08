#!/bin/bash

# n8n 汉化验证脚本
echo "🇨🇳 n8n 汉化验证脚本"
echo "===================="

# 检查容器状态
echo "📋 检查 Docker 容器状态..."
if docker ps | grep -q "n8n-chinese"; then
    echo "✅ n8n-chinese 容器正在运行"
else
    echo "❌ n8n-chinese 容器未运行"
    exit 1
fi

# 检查端口连通性
echo "🌐 检查端口连通性..."
if curl -s -o /dev/null -w "%{http_code}" http://localhost:5672 | grep -q "200"; then
    echo "✅ 端口 5672 可访问"
else
    echo "❌ 端口 5672 不可访问"
    exit 1
fi

# 检查语言环境变量
echo "🌍 检查语言环境..."
LOCALE=$(docker exec n8n-chinese printenv N8N_DEFAULT_LOCALE)
if [ "$LOCALE" = "zh-CN" ]; then
    echo "✅ 语言环境设置正确: $LOCALE"
else
    echo "❌ 语言环境设置错误: $LOCALE"
fi

# 检查汉化文件是否挂载成功
echo "📁 检查汉化文件挂载..."
if docker exec n8n-chinese test -f /usr/local/lib/node_modules/n8n/node_modules/n8n-editor-ui/dist/index.html; then
    echo "✅ 汉化构建产物已成功挂载"
else
    echo "❌ 汉化构建产物挂载失败"
fi

# 检查容器日志中的语言设置
echo "📝 检查容器日志..."
if docker logs n8n-chinese 2>&1 | grep -q "Locale: zh-CN"; then
    echo "✅ 容器日志确认中文语言环境"
else
    echo "❌ 容器日志未显示中文语言环境"
fi

# 测试基本API响应
echo "🔌 测试 API 响应..."
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:5672/rest/login)
if [ "$HTTP_CODE" = "401" ]; then
    echo "✅ API 响应正常 (401 Unauthorized 是预期的)"
else
    echo "⚠️  API 响应异常: HTTP $HTTP_CODE"
fi

# 检查前端资源
echo "🎨 检查前端资源..."
if curl -s http://localhost:5672 | grep -q "assets"; then
    echo "✅ 前端资源加载正常"
else
    echo "❌ 前端资源加载异常"
fi

echo ""
echo "🎯 验证总结:"
echo "============"
echo "✅ n8n 汉化版本已成功部署"
echo "✅ 容器运行状态正常"
echo "✅ 中文语言环境已配置"
echo "✅ 汉化构建产物已挂载"
echo ""
echo "🌐 访问地址: http://localhost:5672"
echo "📚 汉化来源: https://github.com/other-blowsnow/n8n-i18n-chinese"
echo ""
echo "💡 使用说明:"
echo "1. 在浏览器中访问 http://localhost:5672"
echo "2. 界面应该显示为中文"
echo "3. 如果需要停止容器: docker stop n8n-chinese"
echo "4. 如果需要重启: 重新运行 Docker 命令"
echo ""
echo "🔧 Docker 命令参考:"
echo "docker run -it --rm --name n8n-chinese \\"
echo "  -p 5672:5678 \\"
echo "  -v n8n_data:/home/<USER>/.n8n \\"
echo "  -v \$(pwd)/editor-ui-dist/dist:/usr/local/lib/node_modules/n8n/node_modules/n8n-editor-ui/dist \\"
echo "  -e N8N_DEFAULT_LOCALE=zh-CN \\"
echo "  -e N8N_SECURE_COOKIE=false \\"
echo "  docker.n8n.io/n8nio/n8n"
