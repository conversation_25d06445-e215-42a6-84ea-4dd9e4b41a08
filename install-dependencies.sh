#!/bin/bash

# n8n科技新闻聚合器依赖安装脚本
# 适用于现有n8n实例
# 版本: v2.0.0

set -e

echo "🔧 n8n科技新闻聚合器 - 依赖安装脚本"
echo "========================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检测n8n安装方式
detect_n8n_installation() {
    echo -e "${BLUE}🔍 检测n8n安装方式...${NC}"

    if command -v n8n &> /dev/null; then
        echo -e "${GREEN}✅ 检测到全局安装的n8n${NC}"
        INSTALL_TYPE="global"
        return 0
    fi

    if docker ps | grep -q n8n; then
        echo -e "${GREEN}✅ 检测到Docker运行的n8n${NC}"
        INSTALL_TYPE="docker"
        CONTAINER_NAME=$(docker ps --format "table {{.Names}}" | grep n8n | head -1)
        echo -e "${BLUE}📦 容器名称: ${CONTAINER_NAME}${NC}"
        return 0
    fi

    if pgrep -f "n8n" > /dev/null; then
        echo -e "${GREEN}✅ 检测到运行中的n8n进程${NC}"
        INSTALL_TYPE="process"
        return 0
    fi

    echo -e "${RED}❌ 未检测到运行中的n8n实例${NC}"
    echo -e "${YELLOW}请确保n8n正在运行，然后重新执行此脚本${NC}"
    exit 1
}

# 安装依赖包
install_dependencies() {
    echo -e "${BLUE}📦 安装依赖包...${NC}"

    case $INSTALL_TYPE in
        "global")
            echo -e "${BLUE}使用npm全局安装xml2js...${NC}"
            if npm list -g xml2js &> /dev/null; then
                echo -e "${GREEN}✅ xml2js已安装${NC}"
            else
                npm install -g xml2js
                echo -e "${GREEN}✅ xml2js安装完成${NC}"
            fi
            ;;

        "docker")
            echo -e "${BLUE}在Docker容器中安装xml2js...${NC}"
            if docker exec $CONTAINER_NAME npm list xml2js &> /dev/null; then
                echo -e "${GREEN}✅ xml2js已安装${NC}"
            else
                docker exec $CONTAINER_NAME npm install xml2js
                echo -e "${GREEN}✅ xml2js安装完成${NC}"
            fi
            ;;

        "process")
            echo -e "${YELLOW}⚠️  检测到n8n进程运行，但无法确定安装方式${NC}"
            echo -e "${YELLOW}请手动安装xml2js依赖包：${NC}"
            echo -e "  npm install xml2js"
            echo -e "  或在n8n工作目录中执行: npm install xml2js"
            ;;
    esac
}

# 验证安装
verify_installation() {
    echo -e "${BLUE}🔍 验证安装...${NC}"

    case $INSTALL_TYPE in
        "global")
            if npm list -g xml2js &> /dev/null; then
                echo -e "${GREEN}✅ xml2js验证成功${NC}"
            else
                echo -e "${RED}❌ xml2js验证失败${NC}"
                exit 1
            fi
            ;;

        "docker")
            if docker exec $CONTAINER_NAME npm list xml2js &> /dev/null; then
                echo -e "${GREEN}✅ xml2js验证成功${NC}"
            else
                echo -e "${RED}❌ xml2js验证失败${NC}"
                exit 1
            fi
            ;;

        "process")
            echo -e "${YELLOW}⚠️  请手动验证xml2js是否安装成功${NC}"
            ;;
    esac
}

# 显示下一步操作
show_next_steps() {
    echo -e "${GREEN}🎉 依赖安装完成！${NC}"
    echo -e "${BLUE}📋 下一步操作:${NC}"
    echo -e "1. 打开n8n界面 (通常是 http://localhost:5678)"
    echo -e "2. 导入工作流文件: tech-news-aggregator-workflow.json"
    echo -e "3. 配置SMTP邮箱凭据 (Gmail推荐)"
    echo -e "4. 激活工作流"
    echo -e "5. 进行手动测试"
    echo ""
    echo -e "${YELLOW}📚 详细配置请参考: README-科技新闻聚合器配置指南.md${NC}"
    echo ""
    echo -e "${BLUE}🔑 已预配置信息:${NC}"
    echo -e "  - Gemini API密钥: AIzaSyCdXtkWSUFA9cmcFXlFYDHwsqWoZaNi8WE"
    echo -e "  - 收件邮箱: <EMAIL>"
    echo -e "  - 执行时间: 每天10:00"
}

# 主函数
main() {
    detect_n8n_installation
    install_dependencies
    verify_installation
    show_next_steps

    echo -e "${GREEN}✨ 安装完成！祝您使用愉快！${NC}"
}

# 执行主函数
main "$@"
