version: '3.8'

services:
  n8n:
    image: n8nio/n8n:latest
    container_name: n8n-tech-news
    restart: unless-stopped
    ports:
      - "5678:5678"
    environment:
      # 基础配置
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=admin
      - N8N_BASIC_AUTH_PASSWORD=your_secure_password_here

      # 数据库配置（可选，使用PostgreSQL）
      - DB_TYPE=postgresdb
      - DB_POSTGRESDB_HOST=postgres
      - DB_POSTGRESDB_PORT=5432
      - DB_POSTGRESDB_DATABASE=n8n
      - DB_POSTGRESDB_USER=n8n
      - DB_POSTGRESDB_PASSWORD=n8n_password

      # 时区设置
      - TZ=Asia/Shanghai

      # 工作流设置
      - N8N_DEFAULT_LOCALE=zh-CN
      - N8N_TEMPLATES_ENABLED=true
      - N8N_ONBOARDING_FLOW_DISABLED=true

      # 安全设置
      - N8N_SECURE_COOKIE=false
      - N8N_METRICS=true

      # 执行设置
      - EXECUTIONS_PROCESS=main
      - EXECUTIONS_TIMEOUT=3600
      - EXECUTIONS_TIMEOUT_MAX=7200

      # 日志设置
      - N8N_LOG_LEVEL=info
      - N8N_LOG_OUTPUT=console

    volumes:
      - n8n_data:/home/<USER>/.n8n
      - ./workflows:/home/<USER>/.n8n/workflows
      - ./credentials:/home/<USER>/.n8n/credentials
      - ./tech-news-reports:/home/<USER>/reports
    depends_on:
      - postgres
    networks:
      - n8n-network

  postgres:
    image: postgres:15-alpine
    container_name: n8n-postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=n8n
      - POSTGRES_USER=n8n
      - POSTGRES_PASSWORD=n8n_password
      - POSTGRES_NON_ROOT_USER=n8n
      - POSTGRES_NON_ROOT_PASSWORD=n8n_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - n8n-network

  # Redis缓存（可选，用于提高性能）
  redis:
    image: redis:7-alpine
    container_name: n8n-redis
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - n8n-network

volumes:
  n8n_data:
    driver: local
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  n8n-network:
    driver: bridge
